name: CI

on:
  push:
    branches: [ main, master ]
  pull_request:

permissions:
  contents: read

concurrency:
  group: ci-${{ github.ref }}
  cancel-in-progress: true

jobs:
  build-and-test:
    runs-on: ubuntu-latest

    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_DB: benefitlens
          POSTGRES_USER: benefitlens_user
          POSTGRES_PASSWORD: benefitlens_password
        ports:
          - 5432:5432
        options: >-
          --health-cmd="pg_isready -U benefitlens_user -d benefitlens"
          --health-interval=10s
          --health-timeout=5s
          --health-retries=10
      redis:
        image: redis:7-alpine
        ports:
          - 6379:6379
        options: >-
          --health-cmd="redis-cli ping"
          --health-interval=10s
          --health-timeout=5s
          --health-retries=10

    env:
      # Required environment variables for build and tests
      DATABASE_URL: postgresql://benefitlens_user:benefitlens_password@localhost:5432/benefitlens
      REDIS_URL: redis://localhost:6379
      NEXT_PUBLIC_APP_URL: https://ci-build.example.com
      SESSION_SECRET: ci_session_secret_for_build_only
      NODE_ENV: production
      USE_LOCAL_AUTH: true

    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-node@v4
        with:
          node-version: 20
          cache: 'npm'
          cache-dependency-path: package-lock.json
      - name: Install dependencies
        run: npm ci --include=dev

      - name: Install PostgreSQL client
        run: |
          sudo apt-get update
          sudo apt-get install -y postgresql-client

      - name: Wait for Postgres and Redis
        env:
          PGPASSWORD: benefitlens_password
        run: |
          for i in {1..30}; do
            if pg_isready -h localhost -p 5432 -U benefitlens_user -d benefitlens; then
              echo "Postgres is ready"; break
            fi
            echo "Waiting for Postgres... ($i)"; sleep 2
          done
          for i in {1..30}; do
            (echo > /dev/tcp/localhost/6379) >/dev/null 2>&1 && echo "Redis is ready" && break
            echo "Waiting for Redis... ($i)"; sleep 2
          done

      - name: Initialize database schema
        env:
          PGPASSWORD: benefitlens_password
        run: |
          psql -h localhost -U benefitlens_user -d benefitlens -f database/init/01-init.sql

      - name: Run linting
        run: npm run lint
      - name: Build application
        run: npm run build --if-present
      - name: Run tests
        run: npm test -- --run

