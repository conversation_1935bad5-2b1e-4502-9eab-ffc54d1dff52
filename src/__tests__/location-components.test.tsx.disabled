import React from 'react'
import { describe, it, expect, vi, beforeEach } from 'vitest'
import { render, screen, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { LocationInput } from '@/components/ui/location-input'
import { MultiLocationInput } from '@/components/ui/multi-location-input'

// Mock fetch for API calls
const mockFetch = vi.fn()
// @ts-expect-error override global fetch for tests
global.fetch = mockFetch

describe('LocationInput Component', () => {
  beforeEach(() => {
    mockFetch.mockClear()
  })

  it('renders with placeholder text', () => {
    render(
      <LocationInput
        value=""
        onChange={() => {}}
        placeholder="Enter location..."
      />
    )

    expect(screen.getByPlaceholderText('Enter location...')).toBeInTheDocument()
  })

  it('displays the current value', () => {
    render(
      <LocationInput
        value="Berlin, Germany"
        onChange={() => {}}
      />
    )

    expect(screen.getByDisplayValue('Berlin, Germany')).toBeInTheDocument()
  })

  it('calls onChange when input value changes', async () => {
    const user = userEvent.setup()
    const mockOnChange = vi.fn()

    render(
      <LocationInput
        value=""
        onChange={mockOnChange}
      />
    )

    const input = screen.getByRole('textbox')
    await user.type(input, 'Berlin')

    expect(mockOnChange).toHaveBeenCalledWith('Berlin')
  })

  it('fetches suggestions when typing', async () => {
    const user = userEvent.setup()
    const mockSuggestions = {
      suggestions: [
        {
          raw: 'Berlin',
          normalized: 'Berlin, Germany',
          city: 'Berlin',
          country: 'Germany',
          countryCode: 'DE'
        }
      ]
    }

    mockFetch.mockResolvedValue({
      ok: true,
      json: () => Promise.resolve(mockSuggestions)
    } as Response)

    render(
      <LocationInput
        value=""
        onChange={() => {}}
        showSuggestions={true}
      />
    )

    const input = screen.getByRole('textbox')
    await user.type(input, 'Ber')

    await waitFor(() => {
      expect(mockFetch).toHaveBeenCalledWith(
        '/api/locations/suggestions?q=Ber'
      )
    })
  })

  it('displays suggestions dropdown', async () => {
    const user = userEvent.setup()
    const mockSuggestions = {
      suggestions: [
        {
          raw: 'Berlin',
          normalized: 'Berlin, Germany',
          city: 'Berlin',
          country: 'Germany',
          countryCode: 'DE'
        }
      ]
    }

    mockFetch.mockResolvedValue({
      ok: true,
      json: () => Promise.resolve(mockSuggestions)
    } as Response)

    render(
      <LocationInput
        value=""
        onChange={() => {}}
        showSuggestions={true}
      />
    )

    const input = screen.getByRole('textbox')
    await user.type(input, 'Berlin')

    await waitFor(() => {
      expect(screen.getAllByText('Berlin, Germany')[0]).toBeInTheDocument()
    })
  })

  it('selects suggestion when clicked', async () => {
    const user = userEvent.setup()
    const mockOnChange = vi.fn()
    const mockSuggestions = {
      suggestions: [
        {
          raw: 'Berlin',
          normalized: 'Berlin, Germany',
          city: 'Berlin',
          country: 'Germany',
          countryCode: 'DE'
        }
      ]
    }

    mockFetch.mockResolvedValue({
      ok: true,
      json: () => Promise.resolve(mockSuggestions)
    } as Response)

    render(
      <LocationInput
        value=""
        onChange={mockOnChange}
        showSuggestions={true}
      />
    )

    const input = screen.getByRole('textbox')
    await user.type(input, 'Berlin')

    await waitFor(() => {
      expect(screen.getAllByText('Berlin, Germany')[0]).toBeInTheDocument()
    })

    await user.click(screen.getAllByText('Berlin, Germany')[0])

    expect(mockOnChange).toHaveBeenCalledWith(
      'Berlin, Germany',
      expect.objectContaining({
        normalized: 'Berlin, Germany',
        city: 'Berlin',
        country: 'Germany',
        countryCode: 'DE'
      })
    )
  })

  it('shows error message when provided', () => {
    render(
      <LocationInput
        value=""
        onChange={() => {}}
        error="Location is required"
      />
    )

    expect(screen.getByText('Location is required')).toBeInTheDocument()
  })

  it('disables input when disabled prop is true', () => {
    render(
      <LocationInput
        value=""
        onChange={() => {}}
        disabled={true}
      />
    )

    expect(screen.getByRole('textbox')).toBeDisabled()
  })
})

describe('MultiLocationInput Component', () => {
  const mockLocations = [
    {
      location_raw: 'Berlin',
      location_normalized: 'Berlin, Germany',
      city: 'Berlin',
      country: 'Germany',
      country_code: 'DE',
      is_primary: true,
      is_headquarters: true,
      location_type: 'headquarters' as const
    }
  ]

  beforeEach(() => {
    mockFetch.mockClear()
  })

  it('renders existing locations', () => {
    render(
      <MultiLocationInput
        locations={mockLocations}
        onChange={() => {}}
      />
    )

    expect(screen.getByDisplayValue('Berlin')).toBeInTheDocument()
    expect(screen.getByText('Primary')).toBeInTheDocument()
    expect(screen.getByText('HQ')).toBeInTheDocument()
  })

  it('allows adding new locations', async () => {
    const user = userEvent.setup()
    const mockOnChange = vi.fn()

    mockFetch.mockResolvedValue({
      ok: true,
      json: () => Promise.resolve({ suggestions: [] })
    } as Response)

    render(
      <MultiLocationInput
        locations={[]}
        onChange={mockOnChange}
      />
    )

    const addInput = screen.getByPlaceholderText('Add another location...')
    await user.type(addInput, 'Munich')

    const addButton = screen.getByText('Add')
    await user.click(addButton)

    expect(mockOnChange).toHaveBeenCalledWith([
      expect.objectContaining({
        location_raw: 'Munich',
        is_primary: true,
        is_headquarters: true,
        location_type: 'headquarters'
      })
    ])
  })

  it('allows removing locations', async () => {
    const user = userEvent.setup()
    const mockOnChange = vi.fn()
    const multipleLocations = [
      ...mockLocations,
      {
        location_raw: 'Munich',
        location_normalized: 'Munich, Germany',
        city: 'Munich',
        country: 'Germany',
        country_code: 'DE',
        is_primary: false,
        is_headquarters: false,
        location_type: 'office' as const
      }
    ]

    render(
      <MultiLocationInput
        locations={multipleLocations}
        onChange={mockOnChange}
      />
    )

    const removeButtons = screen.getAllByRole('button')
    const removeButton = removeButtons.find(button =>
      button.querySelector('svg') // X icon
    )

    if (removeButton) {
      await user.click(removeButton)
      expect(mockOnChange).toHaveBeenCalled()
    }
  })

  it('updates location type', async () => {
    const user = userEvent.setup()
    const mockOnChange = vi.fn()

    render(
      <MultiLocationInput
        locations={mockLocations}
        onChange={mockOnChange}
      />
    )

    const typeSelect = screen.getByDisplayValue('Headquarters')
    await user.selectOptions(typeSelect, 'office')

    expect(mockOnChange).toHaveBeenCalledWith([
      expect.objectContaining({
        location_type: 'office'
      })
    ])
  })

  it('handles primary location toggle', async () => {
    const user = userEvent.setup()
    const mockOnChange = vi.fn()

    render(
      <MultiLocationInput
        locations={mockLocations}
        onChange={mockOnChange}
      />
    )

    const primaryCheckbox = screen.getByLabelText('Primary location')
    await user.click(primaryCheckbox)

    expect(mockOnChange).toHaveBeenCalled()
  })

  it('handles headquarters toggle', async () => {
    const user = userEvent.setup()
    const mockOnChange = vi.fn()

    render(
      <MultiLocationInput
        locations={mockLocations}
        onChange={mockOnChange}
      />
    )

    const hqCheckbox = screen.getByLabelText('Headquarters')
    await user.click(hqCheckbox)

    expect(mockOnChange).toHaveBeenCalled()
  })

  it('respects maxLocations limit', () => {
    const maxLocations = 2
    const locations = Array(maxLocations).fill(null).map((_, i) => ({
      location_raw: `Location ${i + 1}`,
      location_normalized: `Location ${i + 1}, Country`,
      is_primary: i === 0,
      is_headquarters: i === 0,
      location_type: 'office' as const
    }))

    render(
      <MultiLocationInput
        locations={locations}
        onChange={() => {}}
        maxLocations={maxLocations}
      />
    )

    // Should not show add input when at max
    expect(screen.queryByPlaceholderText('Add another location...')).not.toBeInTheDocument()
  })
})
